-- 升级数据库以支持多跟踪号
-- 扩展现有字段以支持多个跟踪号的存储

-- 1. 扩展tracking_number字段以支持多个跟踪号
ALTER TABLE `orders` 
MODIFY COLUMN `tracking_number` TEXT COMMENT '追踪号(支持多个，用分号分隔)';

-- 2. 添加跟踪号数量字段
ALTER TABLE `orders` 
ADD COLUMN `tracking_count` INT DEFAULT 0 COMMENT '跟踪号数量' AFTER `tracking_number`;

-- 3. 扩展carrier_code字段以支持多个承运商
ALTER TABLE `orders` 
MODIFY COLUMN `carrier_code` VARCHAR(200) DEFAULT '0' COMMENT '承运商代码(支持多个，用分号分隔)';

-- 4. 添加数据来源字段
ALTER TABLE `orders` 
ADD COLUMN `tracking_source` VARCHAR(100) DEFAULT NULL COMMENT '跟踪信息来源(GIGA,17Track,Feishu等)' AFTER `carrier_code`;

-- 5. 添加最后更新时间字段
ALTER TABLE `orders` 
ADD COLUMN `tracking_last_updated` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '跟踪信息最后更新时间' AFTER `tracking_source`;

-- 6. 更新索引
DROP INDEX IF EXISTS `idx_tracking_number` ON `orders`;
ALTER TABLE `orders` 
ADD FULLTEXT INDEX `idx_tracking_number_fulltext` (`tracking_number`);

-- 7. 添加新的索引
ALTER TABLE `orders` 
ADD INDEX `idx_tracking_count` (`tracking_count`),
ADD INDEX `idx_tracking_source` (`tracking_source`),
ADD INDEX `idx_tracking_last_updated` (`tracking_last_updated`);

-- 8. 创建跟踪号详情表（可选，用于存储每个跟踪号的详细信息）
CREATE TABLE IF NOT EXISTS `order_tracking_details` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `order_number` VARCHAR(100) NOT NULL COMMENT '订单号',
    `platform_id` VARCHAR(100) DEFAULT NULL COMMENT '平台订单号',
    `tracking_number` VARCHAR(100) NOT NULL COMMENT '跟踪号',
    `carrier_code` INT DEFAULT 0 COMMENT '承运商代码',
    `carrier_name` VARCHAR(100) DEFAULT NULL COMMENT '承运商名称',
    `status` VARCHAR(50) DEFAULT NULL COMMENT '物流状态',
    `latest_event_description` TEXT DEFAULT NULL COMMENT '最新事件描述',
    `latest_event_location` VARCHAR(200) DEFAULT NULL COMMENT '最新事件位置',
    `latest_event_time` TIMESTAMP NULL DEFAULT NULL COMMENT '最新事件时间',
    `tracking_info` LONGTEXT DEFAULT NULL COMMENT '完整跟踪信息JSON',
    `data_source` VARCHAR(50) DEFAULT NULL COMMENT '数据来源(GIGA/17Track/Feishu)',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX `idx_order_number` (`order_number`),
    INDEX `idx_platform_id` (`platform_id`),
    INDEX `idx_tracking_number` (`tracking_number`),
    INDEX `idx_carrier_code` (`carrier_code`),
    INDEX `idx_status` (`status`),
    INDEX `idx_data_source` (`data_source`),
    INDEX `idx_created_at` (`created_at`),
    INDEX `idx_updated_at` (`updated_at`),
    
    UNIQUE KEY `uk_order_tracking` (`order_number`, `tracking_number`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单跟踪号详情表';

-- 9. 创建视图，方便查询多跟踪号信息
CREATE OR REPLACE VIEW `v_order_tracking_summary` AS
SELECT 
    o.order_number,
    o.platform_channel,
    o.platform_id,
    o.store_account,
    o.order_date,
    o.tracking_number,
    o.tracking_count,
    o.carrier_code,
    o.tracking_source,
    o.status,
    o.latest_event_description,
    o.latest_event_location,
    o.latest_event_time,
    o.tracking_last_updated,
    GROUP_CONCAT(
        DISTINCT otd.tracking_number 
        ORDER BY otd.created_at 
        SEPARATOR ';'
    ) AS all_tracking_numbers,
    GROUP_CONCAT(
        DISTINCT otd.carrier_name 
        ORDER BY otd.created_at 
        SEPARATOR ';'
    ) AS all_carriers,
    COUNT(DISTINCT otd.tracking_number) AS detail_tracking_count
FROM orders o
LEFT JOIN order_tracking_details otd ON (o.order_number = otd.order_number OR o.platform_id = otd.platform_id)
WHERE o.order_date >= '2025-06-14'
GROUP BY o.order_number, o.platform_id
ORDER BY o.order_date DESC;

-- 10. 创建存储过程，用于更新跟踪号统计
DELIMITER //
CREATE OR REPLACE PROCEDURE UpdateTrackingCount()
BEGIN
    -- 更新跟踪号数量
    UPDATE orders 
    SET tracking_count = (
        CASE 
            WHEN tracking_number IS NULL OR tracking_number = '' THEN 0
            ELSE (LENGTH(tracking_number) - LENGTH(REPLACE(tracking_number, ';', '')) + 1)
        END
    )
    WHERE tracking_number IS NOT NULL;
    
    -- 更新跟踪信息来源
    UPDATE orders o
    SET tracking_source = (
        SELECT GROUP_CONCAT(DISTINCT data_source SEPARATOR ',')
        FROM order_tracking_details otd 
        WHERE otd.order_number = o.order_number OR otd.platform_id = o.platform_id
    )
    WHERE EXISTS (
        SELECT 1 FROM order_tracking_details otd 
        WHERE otd.order_number = o.order_number OR otd.platform_id = o.platform_id
    );
END //
DELIMITER ;

-- 11. 显示升级后的表结构
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'orders' 
  AND COLUMN_NAME IN (
    'tracking_number', 
    'tracking_count',
    'carrier_code', 
    'tracking_source',
    'tracking_last_updated',
    'status', 
    'latest_event_description', 
    'latest_event_location', 
    'latest_event_time', 
    'full_track_info'
  )
ORDER BY ORDINAL_POSITION;

-- 12. 显示新创建的表
SHOW CREATE TABLE order_tracking_details;

-- 13. 执行统计更新
CALL UpdateTrackingCount();

-- 14. 显示升级结果统计
SELECT 
    '升级完成统计' AS info,
    COUNT(*) AS total_orders,
    SUM(CASE WHEN tracking_number IS NOT NULL AND tracking_number != '' THEN 1 ELSE 0 END) AS orders_with_tracking,
    SUM(tracking_count) AS total_tracking_numbers,
    AVG(tracking_count) AS avg_tracking_per_order
FROM orders 
WHERE order_date >= '2025-06-14';
