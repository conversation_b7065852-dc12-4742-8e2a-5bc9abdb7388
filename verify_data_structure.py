#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证项目数据结构和流程
"""

import os
import sys
import logging
from dotenv import load_dotenv

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from track_orders import DatabaseManager
from sqlalchemy import text

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def verify_data_structure():
    """验证数据结构"""
    
    print("🔍 验证项目数据结构和流程")
    print("=" * 60)
    
    try:
        # 加载环境变量
        load_dotenv()
        
        # 创建数据库管理器
        db_manager = DatabaseManager()
        
        # 测试数据库连接
        if not db_manager.test_connection():
            print("❌ 数据库连接失败")
            return
        
        print("✅ 数据库连接成功")
        
        with db_manager.engine.connect() as conn:
            # 1. 验证orders表结构
            print(f"\n📊 第一步：验证orders表结构")
            print("=" * 50)
            
            result = conn.execute(text("DESCRIBE orders"))
            orders_columns = result.fetchall()
            
            print("📋 orders表字段:")
            key_fields = ['order_number', 'platform_id', 'tracking_number', 'tracking_count', 'carrier_code', 'tracking_source']
            for column in orders_columns:
                field_name = column[0]
                if field_name in key_fields:
                    print(f"   ✅ {field_name}: {column[1]}")
            
            # 2. 验证order_tracking_details表结构
            print(f"\n📊 第二步：验证order_tracking_details表结构")
            print("=" * 50)
            
            result = conn.execute(text("DESCRIBE order_tracking_details"))
            details_columns = result.fetchall()
            
            print("📋 order_tracking_details表字段:")
            key_fields = ['order_number', 'tracking_number', 'carrier_code', 'carrier_name', 'data_source']
            for column in details_columns:
                field_name = column[0]
                if field_name in key_fields:
                    print(f"   ✅ {field_name}: {column[1]}")
            
            # 3. 查看多跟踪号订单示例
            print(f"\n📦 第三步：查看多跟踪号订单示例")
            print("=" * 50)
            
            # 查找多跟踪号订单
            result = conn.execute(text("""
                SELECT order_number, tracking_number, tracking_count, carrier_code, tracking_source
                FROM orders 
                WHERE tracking_count > 1 
                ORDER BY tracking_count DESC 
                LIMIT 5
            """))
            
            multi_tracking_orders = result.fetchall()
            
            if multi_tracking_orders:
                print("📋 多跟踪号订单示例（orders表）:")
                for i, order in enumerate(multi_tracking_orders, 1):
                    order_no = order[0]
                    tracking_numbers = order[1]
                    tracking_count = order[2]
                    carrier_code = order[3]
                    tracking_source = order[4]
                    
                    print(f"   {i}. 订单: {order_no}")
                    print(f"      跟踪号: {tracking_numbers}")
                    print(f"      数量: {tracking_count}")
                    print(f"      承运商代码: {carrier_code}")
                    print(f"      数据源: {tracking_source}")
                    print()
                    
                    # 查看对应的详情表记录
                    result = conn.execute(text("""
                        SELECT tracking_number, carrier_name, data_source
                        FROM order_tracking_details 
                        WHERE order_number = :order_number
                        ORDER BY created_at
                    """), {'order_number': order_no})
                    
                    details = result.fetchall()
                    print(f"      对应详情表记录 ({len(details)}条):")
                    for j, detail in enumerate(details, 1):
                        print(f"        {j}. 跟踪号: {detail[0]}, 承运商: {detail[1]}, 来源: {detail[2]}")
                    print()
            else:
                print("❌ 未找到多跟踪号订单")
            
            # 4. 统计数据源分布
            print(f"\n📊 第四步：统计数据源分布")
            print("=" * 50)
            
            # orders表数据源统计
            result = conn.execute(text("""
                SELECT tracking_source, COUNT(*) as count
                FROM orders 
                WHERE tracking_number IS NOT NULL AND tracking_number != ''
                GROUP BY tracking_source
                ORDER BY count DESC
            """))
            
            orders_sources = result.fetchall()
            print("📋 orders表数据源统计:")
            for source, count in orders_sources:
                print(f"   {source}: {count} 个订单")
            
            # order_tracking_details表数据源统计
            result = conn.execute(text("""
                SELECT data_source, COUNT(*) as count
                FROM order_tracking_details 
                GROUP BY data_source
                ORDER BY count DESC
            """))
            
            details_sources = result.fetchall()
            print(f"\n📋 order_tracking_details表数据源统计:")
            for source, count in details_sources:
                print(f"   {source}: {count} 条记录")
            
            # 5. 验证数据一致性
            print(f"\n🔍 第五步：验证数据一致性")
            print("=" * 50)
            
            # 检查orders表和details表的记录数一致性
            result = conn.execute(text("""
                SELECT 
                    SUM(tracking_count) as total_tracking_from_orders,
                    (SELECT COUNT(*) FROM order_tracking_details) as total_details_records
                FROM orders 
                WHERE tracking_number IS NOT NULL AND tracking_number != ''
            """))
            
            consistency = result.fetchone()
            orders_total = consistency[0] or 0
            details_total = consistency[1] or 0
            
            print(f"📊 数据一致性检查:")
            print(f"   orders表跟踪号总数: {orders_total}")
            print(f"   details表记录总数: {details_total}")
            
            if orders_total == details_total:
                print(f"   ✅ 数据一致性: 正常")
            else:
                print(f"   ⚠️ 数据一致性: 不匹配 (差异: {abs(orders_total - details_total)})")
            
            # 6. 查看查询逻辑相关字段
            print(f"\n🔍 第六步：验证查询逻辑")
            print("=" * 50)
            
            # 检查eBay订单的platform_id使用情况
            result = conn.execute(text("""
                SELECT COUNT(*) as ebay_count
                FROM orders 
                WHERE platform_channel = 'eBay' AND platform_id IS NOT NULL
            """))
            
            ebay_count = result.fetchone()[0]
            
            # 检查近15天订单数量
            from datetime import datetime, timedelta
            half_month_ago = (datetime.now() - timedelta(days=15)).strftime('%Y-%m-%d')
            
            result = conn.execute(text(f"""
                SELECT COUNT(*) as recent_count
                FROM orders 
                WHERE order_date >= '{half_month_ago}'
            """))
            
            recent_count = result.fetchone()[0]
            
            result = conn.execute(text(f"""
                SELECT COUNT(*) as recent_no_tracking
                FROM orders 
                WHERE order_date >= '{half_month_ago}'
                AND (tracking_number IS NULL OR tracking_number = '')
            """))
            
            recent_no_tracking = result.fetchone()[0]
            
            print(f"📊 查询逻辑验证:")
            print(f"   eBay订单数量: {ebay_count} (使用platform_id)")
            print(f"   近15天订单: {recent_count} 个")
            print(f"   近15天无跟踪号: {recent_no_tracking} 个")
            print(f"   查询效率: {(recent_count-recent_no_tracking)/recent_count*100:.1f}% 订单已有跟踪号")
            
            return True
            
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        logger.error(f"验证失败: {e}")
        return False

def main():
    """主函数"""
    print("🧪 项目数据结构验证工具")
    print("=" * 60)
    
    success = verify_data_structure()
    
    print(f"\n🎯 验证总结:")
    if success:
        print(f"✅ 项目数据结构验证成功")
        
        print(f"\n📋 确认的数据流程:")
        print(f"1. 📊 查询源: orders表的order_number/platform_id")
        print(f"2. 🔍 查询顺序: 飞书 → GIGA SL → GIGA RW")
        print(f"3. 💾 存储方式:")
        print(f"   - orders表: 多跟踪号用分号分隔在同一单元格")
        print(f"   - order_tracking_details表: 每个跟踪号一条记录")
        print(f"4. ⚡ 优化特性: 跳过已有跟踪号 + 近15天查询")
        
        print(f"\n🎉 项目架构清晰，数据结构合理！")
    else:
        print(f"❌ 项目数据结构验证失败")

if __name__ == "__main__":
    main()
