-- 创建tracking表的SQL脚本
-- 用于存储订单追踪信息

CREATE TABLE IF NOT EXISTS `tracking` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_number` varchar(100) NOT NULL COMMENT '订单号',
  `tracking_number` varchar(100) NOT NULL COMMENT '追踪号',
  `carrier_name` varchar(100) DEFAULT NULL COMMENT '承运商名称',
  `carrier_code` int(11) DEFAULT 0 COMMENT '承运商代码',
  `shop_name` varchar(100) DEFAULT NULL COMMENT '店铺名称',
  `platform_name` varchar(50) DEFAULT NULL COMMENT '平台名称',
  `platform_channel` varchar(50) DEFAULT NULL COMMENT '平台渠道',
  `return_tracking_number` varchar(100) DEFAULT NULL COMMENT '退货追踪号',
  `return_shipmethod` varchar(100) DEFAULT NULL COMMENT '退货承运商',
  `status` varchar(50) DEFAULT NULL COMMENT '查询状态',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_order_number` (`order_number`),
  KEY `idx_tracking_number` (`tracking_number`),
  KEY `idx_carrier_code` (`carrier_code`),
  KEY `idx_platform_channel` (`platform_channel`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单追踪信息表';

-- 如果需要，可以添加唯一索引防止重复数据
-- ALTER TABLE `tracking` ADD UNIQUE KEY `unique_order_tracking` (`order_number`, `tracking_number`);
