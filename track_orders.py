#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GIGA Cloud Logistics API 追踪号查询脚本
从Excel文件读取订单号，查询追踪号信息，并将结果保存到Excel文件
"""

import requests
import pandas as pd
import os
from dotenv import load_dotenv
import json
from datetime import datetime
import time
import pymysql
from sqlalchemy import create_engine, text
import logging
from carrier_codes import get_carrier_code, CARRIER_CODE_MAPPING

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DatabaseManager:
    """数据库管理类"""

    def __init__(self):
        # 加载环境变量
        load_dotenv()

        # 数据库连接配置
        self.db_host = os.getenv('DB_HOST', 'localhost')
        self.db_port = int(os.getenv('DB_PORT', 3306))
        self.db_user = os.getenv('DB_USER')
        self.db_password = os.getenv('DB_PASSWORD')
        self.db_name = os.getenv('DB_NAME')

        if not all([self.db_user, self.db_password, self.db_name]):
            raise ValueError("数据库连接信息不完整，请检查.env文件中的DB_USER, DB_PASSWORD, DB_NAME")

        # 创建数据库连接
        # URL编码密码中的特殊字符
        import urllib.parse
        encoded_password = urllib.parse.quote_plus(self.db_password)
        self.connection_string = f"mysql+pymysql://{self.db_user}:{encoded_password}@{self.db_host}:{self.db_port}/{self.db_name}"

        # 创建数据库引擎，配置连接池和超时设置
        self.engine = create_engine(
            self.connection_string,
            echo=False,
            pool_size=10,                    # 连接池大小
            max_overflow=20,                 # 最大溢出连接数
            pool_timeout=30,                 # 获取连接超时时间
            pool_recycle=3600,               # 连接回收时间（1小时）
            pool_pre_ping=True,              # 连接前ping测试
            connect_args={
                'connect_timeout': 60,       # 连接超时
                'read_timeout': 60,          # 读取超时
                'write_timeout': 60,         # 写入超时
                'charset': 'utf8mb4'
            }
        )

    def get_orders_from_database(self, include_with_tracking=True):
        """从orders表获取订单信息

        Args:
            include_with_tracking: 是否包含已有跟踪号的订单
        """
        try:
            # 构建查询条件 - 查询近半个月的订单
            from datetime import datetime, timedelta
            half_month_ago = (datetime.now() - timedelta(days=15)).strftime('%Y-%m-%d')
            today = datetime.now().strftime('%Y-%m-%d')

            where_conditions = [
                "order_number IS NOT NULL",
                f"order_date >= '{half_month_ago}'"
            ]

            # 记录查询日期范围
            logger.info(f"查询日期范围: {half_month_ago} 到 {today} (近15天)")

            if not include_with_tracking:
                logger.info("只查询无跟踪号的订单")

            if not include_with_tracking:
                where_conditions.append("(tracking_number IS NULL OR tracking_number = '')")

            where_clause = " AND ".join(where_conditions)

            query = f"""
            SELECT
                order_number,
                platform_channel,
                platform_id,
                store_account as shop_name,
                platform_channel as platform_name,
                tracking_number
            FROM orders
            WHERE {where_clause}
            """

            with self.engine.connect() as conn:
                result = conn.execute(text(query))
                orders = result.fetchall()

            # 处理订单号逻辑
            processed_orders = []
            for order in orders:
                order_dict = dict(order._mapping)

                # 根据平台渠道决定使用哪个字段作为订单号
                if order_dict['platform_channel'] == 'eBay':
                    actual_order_number = order_dict['platform_id']
                else:
                    actual_order_number = order_dict['order_number']

                if actual_order_number:  # 确保订单号不为空
                    order_dict['actual_order_number'] = str(actual_order_number)
                    processed_orders.append(order_dict)

            logger.info(f"从数据库获取了 {len(processed_orders)} 个有效订单")
            return processed_orders

        except Exception as e:
            logger.error(f"从数据库获取订单失败: {e}")
            raise

    def update_orders_with_tracking(self, tracking_data, batch_size=50):
        """将追踪信息分批更新到orders表中 - 优化版本支持全局分组"""
        try:
            # 过滤出有效的追踪数据
            valid_tracking_data = [
                data for data in tracking_data
                if data.get('trackingNumber', '') not in ['N/A', 'ERROR', '']
            ]

            logger.info(f"开始处理 {len(valid_tracking_data)} 条有效追踪记录")

            # 🔧 关键优化：全局分组处理多跟踪号（避免批处理分割问题）
            logger.info("执行全局分组，合并同一订单的多个跟踪号...")
            order_groups = {}
            for data in valid_tracking_data:
                order_no = data.get('orderNo', '')
                if order_no not in order_groups:
                    order_groups[order_no] = []
                order_groups[order_no].append(data)

            logger.info(f"发现 {len(order_groups)} 个不同的订单")

            # 统计多跟踪号订单
            multi_tracking_orders = 0
            total_tracking_numbers = 0
            for order_no, tracking_items in order_groups.items():
                valid_tracking_count = sum(1 for item in tracking_items
                                         if item.get('trackingNumber', '') not in ['N/A', 'ERROR', ''])
                total_tracking_numbers += valid_tracking_count
                if valid_tracking_count > 1:
                    multi_tracking_orders += 1
                    logger.info(f"🎯 发现多跟踪号订单: {order_no} ({valid_tracking_count}个跟踪号)")

            logger.info(f"📊 统计结果: {multi_tracking_orders} 个多跟踪号订单，总计 {total_tracking_numbers} 个跟踪号")

            # 准备批量更新数据
            update_data = []
            for order_no, tracking_items in order_groups.items():
                processed_order = self._prepare_order_update_data(order_no, tracking_items)
                if processed_order:
                    update_data.append(processed_order)

            # 分批更新数据库
            total_updated = self._batch_update_database(update_data, batch_size)

            logger.info(f"所有批次处理完成，总计更新 {total_updated} 个订单的追踪信息")
            logger.info(f"其中包含 {multi_tracking_orders} 个多跟踪号订单")

            return total_updated

        except Exception as e:
            logger.error(f"分批更新orders表追踪信息失败: {e}")
            raise

    def _prepare_order_update_data(self, order_no, tracking_items):
        """准备单个订单的更新数据"""
        try:
            # 收集该订单的所有跟踪号
            tracking_numbers = []
            carrier_names = []

            for data in tracking_items:
                tracking_number = data.get('trackingNumber', '')
                if tracking_number and tracking_number not in ['N/A', 'ERROR', '']:
                    tracking_numbers.append(tracking_number)
                    carrier_name = data.get('carrierName', '')
                    if carrier_name:
                        carrier_names.append(carrier_name)

            if not tracking_numbers:
                return None

            # 确定使用哪个字段作为查询条件
            platform_channel = tracking_items[0].get('platform_channel', '')
            where_condition = "platform_id = :order_no" if platform_channel == 'eBay' else "order_number = :order_no"

            # 准备更新数据
            tracking_numbers_str = ';'.join(tracking_numbers)
            tracking_count = len(tracking_numbers)
            primary_carrier = carrier_names[0] if carrier_names else ''
            carrier_code = get_carrier_code(primary_carrier)

            return {
                'order_no': order_no,
                'tracking_numbers_str': tracking_numbers_str,
                'tracking_count': tracking_count,
                'carrier_code': carrier_code,
                'where_condition': where_condition,
                'tracking_items': tracking_items,
                'platform_channel': platform_channel
            }

        except Exception as e:
            logger.error(f"准备订单 {order_no} 更新数据失败: {e}")
            return None

    def _batch_update_database(self, update_data, batch_size):
        """分批更新数据库"""
        total_updated = 0
        total_batches = (len(update_data) + batch_size - 1) // batch_size

        for batch_num in range(0, len(update_data), batch_size):
            batch_data = update_data[batch_num:batch_num + batch_size]
            current_batch = (batch_num // batch_size) + 1

            logger.info(f"处理第 {current_batch}/{total_batches} 批，包含 {len(batch_data)} 个订单")

            batch_updated = self._update_batch_optimized(batch_data)
            total_updated += batch_updated

            logger.info(f"第 {current_batch} 批完成，更新了 {batch_updated} 条记录")

            # 批次间短暂休息，避免数据库压力
            if current_batch < total_batches:
                time.sleep(0.5)

        return total_updated

    def _update_batch_optimized(self, batch_data):
        """优化版本的批次更新"""
        updated_count = 0

        try:
            with self.engine.connect() as conn:
                trans = conn.begin()

                try:
                    for order_data in batch_data:
                        # 更新主表
                        update_sql = f"""
                        UPDATE orders
                        SET
                            tracking_number = :tracking_number,
                            tracking_count = :tracking_count,
                            carrier_code = :carrier_code,
                            tracking_source = :tracking_source,
                            tracking_last_updated = NOW()
                        WHERE {order_data['where_condition']}
                        """

                        params = {
                            'order_no': order_data['order_no'],
                            'tracking_number': order_data['tracking_numbers_str'],
                            'tracking_count': order_data['tracking_count'],
                            'carrier_code': order_data['carrier_code'],
                            'tracking_source': 'GIGA-API'
                        }

                        result = conn.execute(text(update_sql), params)
                        if result.rowcount > 0:
                            updated_count += 1

                            # 先删除该订单的旧详情记录
                            delete_sql = f"DELETE FROM order_tracking_details WHERE {order_data['where_condition'].replace(':order_no', ':del_order_no')}"
                            conn.execute(text(delete_sql), {'del_order_no': order_data['order_no']})

                            # 插入详情表记录
                            for data in order_data['tracking_items']:
                                tracking_number = data.get('trackingNumber', '')
                                if tracking_number and tracking_number not in ['N/A', 'ERROR', '']:
                                    carrier_name = data.get('carrierName', '')
                                    carrier_code_detail = get_carrier_code(carrier_name)

                                    # 确保carrier_name不为空，如果为空则根据carrier_code生成
                                    if not carrier_name or carrier_name in ['', 'N/A', 'ERROR']:
                                        from carrier_codes import get_carrier_name_by_code
                                        carrier_name = get_carrier_name_by_code(carrier_code_detail)

                                    detail_sql = """
                                    INSERT INTO order_tracking_details
                                    (order_number, tracking_number, carrier_code, carrier_name,
                                     status, data_source, created_at, updated_at)
                                    VALUES
                                    (:order_number, :tracking_number, :carrier_code, :carrier_name,
                                     :status, :data_source, NOW(), NOW())
                                    ON DUPLICATE KEY UPDATE
                                    carrier_code = VALUES(carrier_code),
                                    carrier_name = VALUES(carrier_name),
                                    status = VALUES(status),
                                    data_source = VALUES(data_source),
                                    updated_at = NOW()
                                    """

                                    detail_params = {
                                        'order_number': order_data['order_no'],
                                        'tracking_number': tracking_number,
                                        'carrier_code': carrier_code_detail,
                                        'carrier_name': carrier_name,
                                        'status': 'SUCCESS',
                                        'data_source': 'GIGA-API'
                                    }

                                    conn.execute(text(detail_sql), detail_params)

                    # 提交批次事务
                    trans.commit()

                except Exception as e:
                    # 回滚批次事务
                    trans.rollback()
                    logger.error(f"批次更新失败，已回滚: {e}")
                    raise

        except Exception as e:
            logger.error(f"批次数据库操作失败: {e}")
            raise

        return updated_count

    def _update_batch(self, batch_data):
        """更新单个批次的数据"""
        updated_count = 0

        try:
            # 使用新的连接处理每个批次
            with self.engine.connect() as conn:
                # 开始事务
                trans = conn.begin()

                try:
                    # 按订单分组处理多跟踪号
                    order_groups = {}
                    for data in batch_data:
                        order_no = data.get('orderNo', '')
                        if order_no not in order_groups:
                            order_groups[order_no] = []
                        order_groups[order_no].append(data)

                    # 处理每个订单的所有跟踪号
                    for order_no, tracking_items in order_groups.items():
                        # 收集该订单的所有跟踪号
                        tracking_numbers = []
                        carrier_names = []

                        for data in tracking_items:
                            tracking_number = data.get('trackingNumber', '')
                            if tracking_number and tracking_number not in ['N/A', 'ERROR', '']:
                                tracking_numbers.append(tracking_number)
                                carrier_name = data.get('carrierName', '')
                                if carrier_name:
                                    carrier_names.append(carrier_name)

                        if not tracking_numbers:
                            continue

                        # 确定使用哪个字段作为查询条件
                        platform_channel = tracking_items[0].get('platform_channel', '')
                        if platform_channel == 'eBay':
                            where_condition = "platform_id = :order_no"
                        else:
                            where_condition = "order_number = :order_no"

                        # 准备主表更新数据
                        tracking_numbers_str = ';'.join(tracking_numbers)
                        tracking_count = len(tracking_numbers)
                        primary_carrier = carrier_names[0] if carrier_names else ''
                        carrier_code = get_carrier_code(primary_carrier)

                        # 更新主表
                        update_sql = f"""
                        UPDATE orders
                        SET
                            tracking_number = :tracking_number,
                            tracking_count = :tracking_count,
                            carrier_code = :carrier_code,
                            tracking_source = :tracking_source,
                            tracking_last_updated = NOW()
                        WHERE {where_condition}
                        """

                        params = {
                            'order_no': order_no,
                            'tracking_number': tracking_numbers_str,
                            'tracking_count': tracking_count,
                            'carrier_code': carrier_code,
                            'tracking_source': 'GIGA-API'
                        }

                        result = conn.execute(text(update_sql), params)
                        if result.rowcount > 0:
                            updated_count += 1

                            # 先删除该订单的旧详情记录
                            delete_sql = f"DELETE FROM order_tracking_details WHERE {where_condition.replace(':order_no', ':del_order_no')}"
                            conn.execute(text(delete_sql), {'del_order_no': order_no})

                            # 插入详情表记录
                            for i, data in enumerate(tracking_items):
                                tracking_number = data.get('trackingNumber', '')
                                if tracking_number and tracking_number not in ['N/A', 'ERROR', '']:
                                    carrier_name = data.get('carrierName', '')
                                    carrier_code_detail = get_carrier_code(carrier_name)

                                    # 确保carrier_name不为空，如果为空则根据carrier_code生成
                                    if not carrier_name or carrier_name in ['', 'N/A', 'ERROR']:
                                        from carrier_codes import get_carrier_name_by_code
                                        carrier_name = get_carrier_name_by_code(carrier_code_detail)

                                    detail_sql = """
                                    INSERT INTO order_tracking_details
                                    (order_number, tracking_number, carrier_code, carrier_name,
                                     status, data_source, created_at, updated_at)
                                    VALUES
                                    (:order_number, :tracking_number, :carrier_code, :carrier_name,
                                     :status, :data_source, NOW(), NOW())
                                    ON DUPLICATE KEY UPDATE
                                    carrier_code = VALUES(carrier_code),
                                    carrier_name = VALUES(carrier_name),
                                    status = VALUES(status),
                                    data_source = VALUES(data_source),
                                    updated_at = NOW()
                                    """

                                    detail_params = {
                                        'order_number': order_no,
                                        'tracking_number': tracking_number,
                                        'carrier_code': carrier_code_detail,
                                        'carrier_name': carrier_name,
                                        'status': 'SUCCESS',
                                        'data_source': 'GIGA-API'
                                    }

                                    conn.execute(text(detail_sql), detail_params)

                    # 提交批次事务
                    trans.commit()

                except Exception as e:
                    # 回滚批次事务
                    trans.rollback()
                    logger.error(f"批次更新失败，已回滚: {e}")
                    raise

        except Exception as e:
            logger.error(f"批次数据库操作失败: {e}")
            raise

        return updated_count

    def fix_multi_tracking_from_excel(self, excel_file_path=None):
        """基于Excel报告批量修复所有多跟踪号订单"""
        try:
            import pandas as pd
            import glob

            # 查找Excel文件
            if not excel_file_path:
                excel_files = glob.glob('tracking_results_*.xlsx')
                if not excel_files:
                    logger.error("未找到Excel报告文件")
                    return 0
                excel_file_path = max(excel_files, key=os.path.getctime)

            logger.info(f"🔧 开始基于Excel报告修复多跟踪号订单: {excel_file_path}")

            # 读取Excel数据
            df = pd.read_excel(excel_file_path, sheet_name='所有追踪号详情')
            logger.info(f"📊 Excel中共有 {len(df)} 条记录")

            # 按订单分组
            order_groups = df.groupby('订单号')
            multi_tracking_orders = []

            for order_no, group in order_groups:
                valid_tracking = group[
                    (group['物流追踪号'].notna()) &
                    (group['物流追踪号'] != 'N/A') &
                    (group['物流追踪号'] != 'ERROR')
                ]

                if len(valid_tracking) > 1:
                    multi_tracking_orders.append({
                        'order_no': order_no,
                        'tracking_count': len(valid_tracking),
                        'tracking_data': valid_tracking.to_dict('records')
                    })

            logger.info(f"🎯 发现 {len(multi_tracking_orders)} 个多跟踪号订单需要修复")

            if not multi_tracking_orders:
                logger.info("✅ 没有需要修复的多跟踪号订单")
                return 0

            # 批量修复
            fixed_count = 0
            for order_info in multi_tracking_orders:
                try:
                    success = self._fix_single_order_from_excel(order_info)
                    if success:
                        fixed_count += 1
                        logger.info(f"✅ 修复订单 {order_info['order_no']} ({order_info['tracking_count']}个跟踪号)")
                    else:
                        logger.error(f"❌ 修复订单 {order_info['order_no']} 失败")
                except Exception as e:
                    logger.error(f"❌ 修复订单 {order_info['order_no']} 时出错: {e}")

            logger.info(f"🎉 批量修复完成，成功修复 {fixed_count}/{len(multi_tracking_orders)} 个订单")
            return fixed_count

        except Exception as e:
            logger.error(f"批量修复多跟踪号订单失败: {e}")
            return 0

    def _fix_single_order_from_excel(self, order_info):
        """修复单个订单的多跟踪号"""
        try:
            order_no = order_info['order_no']
            tracking_data = []

            for record in order_info['tracking_data']:
                tracking_number = str(record['物流追踪号']).strip()
                carrier_name = str(record['承运商']).strip()

                if tracking_number and tracking_number not in ['nan', 'N/A', 'ERROR']:
                    tracking_data.append({
                        'tracking_number': tracking_number,
                        'carrier_name': carrier_name,
                        'status': str(record.get('状态', 'SUCCESS'))
                    })

            if not tracking_data:
                return False

            # 更新数据库
            with self.engine.connect() as conn:
                trans = conn.begin()

                try:
                    # 更新主表
                    tracking_numbers_str = ';'.join([data['tracking_number'] for data in tracking_data])
                    tracking_count = len(tracking_data)
                    primary_carrier = tracking_data[0]['carrier_name']
                    carrier_code = get_carrier_code(primary_carrier)

                    update_sql = """
                    UPDATE orders
                    SET
                        tracking_number = :tracking_number,
                        tracking_count = :tracking_count,
                        carrier_code = :carrier_code,
                        tracking_source = :tracking_source,
                        tracking_last_updated = NOW()
                    WHERE order_number = :order_number OR platform_id = :order_number
                    """

                    result = conn.execute(text(update_sql), {
                        'order_number': order_no,
                        'tracking_number': tracking_numbers_str,
                        'tracking_count': tracking_count,
                        'carrier_code': carrier_code,
                        'tracking_source': 'GIGA-API'
                    })

                    if result.rowcount == 0:
                        logger.warning(f"订单 {order_no} 在数据库中不存在")
                        trans.rollback()
                        return False

                    # 更新详情表
                    conn.execute(text("DELETE FROM order_tracking_details WHERE order_number = :order_number"),
                               {'order_number': order_no})

                    for data in tracking_data:
                        carrier_code_detail = get_carrier_code(data['carrier_name'])

                        detail_sql = """
                        INSERT INTO order_tracking_details
                        (order_number, tracking_number, carrier_code, carrier_name,
                         status, data_source, created_at, updated_at)
                        VALUES
                        (:order_number, :tracking_number, :carrier_code, :carrier_name,
                         :status, :data_source, NOW(), NOW())
                        """

                        conn.execute(text(detail_sql), {
                            'order_number': order_no,
                            'tracking_number': data['tracking_number'],
                            'carrier_code': carrier_code_detail,
                            'carrier_name': data['carrier_name'],
                            'status': data['status'],
                            'data_source': 'GIGA-API'
                        })

                    trans.commit()
                    return True

                except Exception as e:
                    trans.rollback()
                    logger.error(f"修复订单 {order_no} 数据库操作失败: {e}")
                    return False

        except Exception as e:
            logger.error(f"修复订单 {order_info['order_no']} 失败: {e}")
            return False

    def validate_data_consistency(self):
        """验证数据一致性"""
        try:
            logger.info("🔍 开始数据一致性验证...")

            with self.engine.connect() as conn:
                # 1. 检查主表和详情表的数量一致性
                result = conn.execute(text("""
                    SELECT
                        o.order_number,
                        o.tracking_count,
                        o.tracking_number,
                        COUNT(d.tracking_number) as detail_count
                    FROM orders o
                    LEFT JOIN order_tracking_details d ON o.order_number = d.order_number
                    WHERE o.tracking_number IS NOT NULL AND o.tracking_number != ''
                    GROUP BY o.order_number, o.tracking_count, o.tracking_number
                    HAVING o.tracking_count != COUNT(d.tracking_number)
                """))

                inconsistent_orders = result.fetchall()

                if inconsistent_orders:
                    logger.warning(f"⚠️ 发现 {len(inconsistent_orders)} 个订单的数量不一致:")
                    for order in inconsistent_orders[:5]:  # 只显示前5个
                        logger.warning(f"   订单 {order[0]}: 主表显示{order[1]}个，详情表有{order[3]}个")
                else:
                    logger.info("✅ 数量一致性检查通过")

                # 2. 检查跟踪号内容一致性
                result = conn.execute(text("""
                    SELECT
                        o.order_number,
                        o.tracking_number as main_tracking,
                        GROUP_CONCAT(d.tracking_number ORDER BY d.tracking_number SEPARATOR ';') as detail_tracking
                    FROM orders o
                    LEFT JOIN order_tracking_details d ON o.order_number = d.order_number
                    WHERE o.tracking_number IS NOT NULL AND o.tracking_number != ''
                    GROUP BY o.order_number, o.tracking_number
                """))

                content_issues = []
                for row in result:
                    main_tracking = set(row[1].split(';')) if row[1] else set()
                    detail_tracking = set(row[2].split(';')) if row[2] else set()

                    if main_tracking != detail_tracking:
                        content_issues.append({
                            'order_number': row[0],
                            'main_tracking': row[1],
                            'detail_tracking': row[2]
                        })

                if content_issues:
                    logger.warning(f"⚠️ 发现 {len(content_issues)} 个订单的跟踪号内容不一致:")
                    for issue in content_issues[:5]:  # 只显示前5个
                        logger.warning(f"   订单 {issue['order_number']}: 内容不匹配")
                else:
                    logger.info("✅ 跟踪号内容一致性检查通过")

                # 3. 统计报告
                result = conn.execute(text("""
                    SELECT
                        COUNT(*) as total_orders,
                        SUM(CASE WHEN tracking_count > 1 THEN 1 ELSE 0 END) as multi_tracking_orders,
                        SUM(tracking_count) as total_tracking_numbers
                    FROM orders
                    WHERE tracking_number IS NOT NULL AND tracking_number != ''
                """))

                stats = result.fetchone()
                logger.info(f"📊 数据统计:")
                logger.info(f"   总订单数: {stats[0]}")
                logger.info(f"   多跟踪号订单: {stats[1]}")
                logger.info(f"   总跟踪号数: {stats[2]}")

                return {
                    'quantity_issues': len(inconsistent_orders),
                    'content_issues': len(content_issues),
                    'total_orders': stats[0],
                    'multi_tracking_orders': stats[1],
                    'total_tracking_numbers': stats[2]
                }

        except Exception as e:
            logger.error(f"数据一致性验证失败: {e}")
            return None

    def get_carrier_code_info(self, carrier_name):
        """获取承运商代码（使用外部配置）"""
        return get_carrier_code(carrier_name)

    def test_connection(self):
        """测试数据库连接"""
        try:
            with self.engine.connect() as conn:
                result = conn.execute(text("SELECT VERSION()"))
                version = result.fetchone()[0]
                logger.info(f"数据库连接成功！MySQL版本: {version}")

                # 检查orders表
                result = conn.execute(text("SHOW TABLES LIKE 'orders'"))
                if result.fetchone():
                    result = conn.execute(text("SELECT COUNT(*) FROM orders"))
                    count = result.fetchone()[0]
                    logger.info(f"orders表存在，包含 {count} 条记录")
                else:
                    logger.warning("orders表不存在")

                # 检查tracking表
                result = conn.execute(text("SHOW TABLES LIKE 'tracking'"))
                if result.fetchone():
                    logger.info("tracking表存在")
                else:
                    logger.warning("tracking表不存在，请运行create_tracking_table.sql创建")

                return True
        except Exception as e:
            logger.error(f"数据库连接测试失败: {e}")
            return False

class FeishuAPIClient:
    """飞书API客户端类"""

    def __init__(self):
        # 加载环境变量
        load_dotenv()

        # 飞书API配置
        self.feishu_app_id = os.getenv('FEISHU_APP_ID')
        self.feishu_app_secret = os.getenv('FEISHU_APP_SECRET')
        self.feishu_base_id = 'EZMpb65tKasnixsLNTPcbBwlnmd'
        self.feishu_table_id = 'tblOXxIyMI8YHRmb'
        self.feishu_access_token = None

        # 检查配置
        self.has_feishu_config = bool(self.feishu_app_id and self.feishu_app_secret)

        print(f"🔧 飞书API配置:")
        print(f"   飞书配置: {'✅ 已配置' if self.has_feishu_config else '❌ 未配置'}")

    def get_feishu_access_token(self):
        """获取飞书访问令牌"""
        if not self.has_feishu_config:
            return None

        url = "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal"
        headers = {"Content-Type": "application/json; charset=utf-8"}
        data = {
            "app_id": self.feishu_app_id,
            "app_secret": self.feishu_app_secret
        }

        try:
            response = requests.post(url, headers=headers, json=data, timeout=10)
            result = response.json()

            if result.get('code') == 0:
                self.feishu_access_token = result.get('tenant_access_token')
                return self.feishu_access_token
            else:
                logger.error(f"飞书访问令牌获取失败: {result.get('msg')}")
                return None

        except Exception as e:
            logger.error(f"飞书访问令牌请求失败: {e}")
            return None

    def normalize_order_number(self, order_number):
        """标准化订单号，移除末尾的后缀"""
        if not order_number:
            return order_number

        # 移除末尾的 -1, -2, -R, -DF 等后缀
        # 但保留正常的订单号格式（如 111-1234567-1234567）
        import re
        order_stripped = order_number.strip()

        # 只移除特定模式的后缀：
        # -1, -2 等单独的数字后缀
        # -R, -DF 等字母后缀
        # 但不移除正常订单号中间的数字部分
        normalized = re.sub(r'-([1-9]|R|DF)$', '', order_stripped)
        return normalized

    def normalize_carrier_name(self, carrier_name):
        """标准化承运商名称"""
        if not carrier_name:
            return carrier_name

        carrier_upper = carrier_name.upper().strip()

        # 将所有FedEx相关的承运商统一为FedEx
        if any(fedex_variant in carrier_upper for fedex_variant in ['FEDEX-HME', 'FEDEX-W003', 'FEDEX']):
            return 'FedEx'

        return carrier_name

    def get_all_feishu_records(self):
        """获取所有飞书表格记录"""
        if not self.has_feishu_config:
            return {}

        access_token = self.get_feishu_access_token()
        if not access_token:
            return {}

        url = f"https://open.feishu.cn/open-apis/bitable/v1/apps/{self.feishu_base_id}/tables/{self.feishu_table_id}/records"
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json; charset=utf-8"
        }

        all_records = []
        page_token = None

        try:
            while True:
                params = {"page_size": 500}
                if page_token:
                    params["page_token"] = page_token

                response = requests.get(url, headers=headers, params=params, timeout=30)
                result = response.json()

                if response.status_code == 200 and result.get('code') == 0:
                    records = result.get('data', {}).get('items', [])
                    all_records.extend(records)

                    # 检查是否还有更多数据
                    has_more = result.get('data', {}).get('has_more', False)
                    if has_more:
                        page_token = result.get('data', {}).get('page_token')
                    else:
                        break
                else:
                    logger.error(f"获取飞书记录失败: {result.get('msg')}")
                    break

            # 创建飞书记录的字典，以标准化订单号为键
            feishu_dict = {}

            for record in all_records:
                fields = record.get('fields', {})
                raw_order_number = fields.get('订单号', '')
                tracking_number = fields.get('跟踪单号', '')
                raw_carrier = fields.get('承运商', '')

                if raw_order_number and tracking_number:
                    # 标准化订单号和承运商
                    order_number = self.normalize_order_number(raw_order_number)
                    carrier = self.normalize_carrier_name(raw_carrier)

                    # 支持多个跟踪号（用逗号或分号分隔）
                    tracking_numbers = []
                    for sep in [',', ';', '|', '\n']:
                        if sep in tracking_number:
                            tracking_numbers = [num.strip() for num in tracking_number.split(sep)]
                            break
                    else:
                        tracking_numbers = [tracking_number.strip()]

                    # 过滤空值
                    tracking_numbers = [num for num in tracking_numbers if num]

                    if tracking_numbers:
                        if order_number in feishu_dict:
                            # 合并跟踪号
                            existing_numbers = feishu_dict[order_number]['tracking_numbers']
                            for num in tracking_numbers:
                                if num not in existing_numbers:
                                    existing_numbers.append(num)
                        else:
                            feishu_dict[order_number] = {
                                'tracking_numbers': tracking_numbers,
                                'carrier': carrier,
                                'raw_order_number': raw_order_number  # 保留原始订单号用于调试
                            }

            logger.info(f"飞书表格获取成功，找到 {len(feishu_dict)} 个订单的跟踪信息")

            # 输出一些调试信息
            logger.info("飞书数据示例（前5个）:")
            count = 0
            for order_no, info in feishu_dict.items():
                if count >= 5:
                    break
                logger.info(f"  {order_no} (原始: {info['raw_order_number']}) -> {len(info['tracking_numbers'])}个跟踪号, 承运商: {info['carrier']}")
                count += 1

            return feishu_dict

        except Exception as e:
            logger.error(f"飞书表格请求失败: {e}")
            return {}

class GigaAPIClient:
    """GIGA API客户端类"""
    
    def __init__(self):
        # 加载环境变量
        load_dotenv()

        self.base_url = "https://api.gigacloudlogistics.com"

        # SL账号API配置（优先使用）
        self.sl_client_id = os.getenv('SL_CLIENT_ID')
        self.sl_client_secret = os.getenv('SL_CLIENT_SECRET')

        # RW账号API配置（备用）
        self.rw_client_id = os.getenv('RW_CLIENT_ID') or os.getenv('CLIENT_ID')  # 兼容原有配置
        self.rw_client_secret = os.getenv('RW_CLIENT_SECRET') or os.getenv('CLIENT_SECRET')  # 兼容原有配置

        if not self.rw_client_id or not self.rw_client_secret:
            raise ValueError("请在.env文件中设置RW账号的CLIENT_ID和CLIENT_SECRET")

        # 检查是否有SL账号配置
        self.has_sl_account = bool(self.sl_client_id and self.sl_client_secret)

        # 令牌缓存 - 分别缓存两个账号的令牌
        self.sl_access_token = None
        self.sl_token_expires_at = 0
        self.rw_access_token = None
        self.rw_token_expires_at = 0

        # 统计信息
        self.sl_success_count = 0
        self.rw_success_count = 0
        self.total_failed_count = 0

        print(f"🔧 API配置:")
        print(f"   SL账号: {'✅ 已配置' if self.has_sl_account else '❌ 未配置'}")
        print(f"   RW账号: ✅ 已配置")
    
    def get_access_token(self, account_type='rw'):
        """获取访问令牌

        Args:
            account_type (str): 账号类型，'sl' 或 'rw'
        """

        if account_type == 'sl':
            if not self.has_sl_account:
                return None
            # 检查SL账号令牌是否仍然有效（留5分钟缓冲）
            if self.sl_access_token and time.time() < (self.sl_token_expires_at - 300):
                return self.sl_access_token
            client_id = self.sl_client_id
            client_secret = self.sl_client_secret
        else:
            # 检查RW账号令牌是否仍然有效（留5分钟缓冲）
            if self.rw_access_token and time.time() < (self.rw_token_expires_at - 300):
                return self.rw_access_token
            client_id = self.rw_client_id
            client_secret = self.rw_client_secret
        
        url = f"{self.base_url}/api-auth-v1/oauth/token"

        payload = {
            "grant_type": "client_credentials",
            "client_id": client_id,
            "client_secret": client_secret
        }

        headers = {
            "Content-Type": "application/x-www-form-urlencoded"
        }

        try:
            response = requests.post(url, data=payload, headers=headers)
            response.raise_for_status()

            token_data = response.json()
            access_token = token_data['access_token']
            expires_at = time.time() + token_data['expires_in']

            # 根据账号类型存储令牌
            if account_type == 'sl':
                self.sl_access_token = access_token
                self.sl_token_expires_at = expires_at
                print(f"✅ SL账号访问令牌获取成功")
            else:
                self.rw_access_token = access_token
                self.rw_token_expires_at = expires_at
                print(f"✅ RW账号访问令牌获取成功")

            print(f"🕒 令牌将在 {token_data['expires_in']} 秒后过期")

            return access_token

        except requests.exceptions.RequestException as e:
            print(f"❌ 获取{account_type.upper()}账号访问令牌失败: {e}")
            if hasattr(e, 'response') and e.response is not None:
                print(f"响应: {e.response.text}")
            return None
    
    def query_tracking_numbers(self, orders_data):
        """查询订单追踪号 - 双账号级联查询"""

        all_results = []
        failed_orders = []  # 存储SL账号查询失败的订单
        total_orders = len(orders_data)

        print(f"🚀 开始双账号级联查询 {total_orders} 个订单")

        # 第一轮：使用SL账号查询（如果配置了）
        if self.has_sl_account:
            print(f"📊 第一轮：使用SL账号查询...")
            sl_token = self.get_access_token('sl')
            if sl_token:
                sl_results, sl_failed = self._query_with_account(orders_data, 'sl', sl_token)
                all_results.extend(sl_results)
                failed_orders = sl_failed
                print(f"✅ SL账号查询完成: 成功 {len(sl_results)} 个，失败 {len(sl_failed)} 个")
            else:
                print(f"❌ SL账号令牌获取失败，跳过SL账号查询")
                failed_orders = orders_data
        else:
            print(f"⚠️  未配置SL账号，跳过第一轮查询")
            failed_orders = orders_data

        # 第二轮：使用RW账号查询失败的订单
        if failed_orders:
            print(f"📊 第二轮：使用RW账号查询剩余 {len(failed_orders)} 个订单...")
            rw_token = self.get_access_token('rw')
            if rw_token:
                rw_results, rw_failed = self._query_with_account(failed_orders, 'rw', rw_token)
                all_results.extend(rw_results)
                print(f"✅ RW账号查询完成: 成功 {len(rw_results)} 个，失败 {len(rw_failed)} 个")
                self.total_failed_count = len(rw_failed)
            else:
                print(f"❌ RW账号令牌获取失败")
                self.total_failed_count = len(failed_orders)

        # 打印统计信息
        print(f"\n📈 查询统计:")
        print(f"   SL账号成功: {self.sl_success_count}")
        print(f"   RW账号成功: {self.rw_success_count}")
        print(f"   总计失败: {self.total_failed_count}")
        print(f"   总计订单: {total_orders}")

        return all_results

    def _query_with_account(self, orders_data, account_type, token):
        """使用指定账号查询订单追踪号"""

        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }

        results = []
        failed_orders = []
        total_orders = len(orders_data)

        for i, order_data in enumerate(orders_data, 1):
            # 支持两种输入格式：字符串（旧格式）或字典（新格式）
            if isinstance(order_data, str):
                order_no = order_data
                shop_name = ''
                platform_name = ''
                platform_channel = ''
            else:
                order_no = order_data.get('actual_order_number', '')
                shop_name = order_data.get('shop_name', '')
                platform_name = order_data.get('platform_name', '')
                platform_channel = order_data.get('platform_channel', '')
            try:
                progress_percent = (i / total_orders) * 100
                print(f"📦 {account_type.upper()}账号查询订单 {i}/{total_orders} ({progress_percent:.1f}%): {order_no}")

                # 使用GET方法查询单个订单
                url = f"{self.base_url}/api-b2b-v1/order/track-no?orderno={order_no}"

                response = requests.get(url, headers=headers)
                response.raise_for_status()

                result = response.json()

                # 处理API响应数据
                if result.get('success', False):
                    tracking_data = result.get('data', [])
                    return_info = result.get('returnInfo', [])

                    # 如果有追踪信息
                    if tracking_data:
                        for track_item in tracking_data:
                            order_result = {
                                'orderNo': order_no,
                                'trackingNumber': track_item.get('trackingNumber', ''),
                                'carrierName': track_item.get('carrierName', ''),
                                'returnTrackingNumber': '',
                                'returnShipmethod': '',
                                'status': 'SUCCESS',
                                'error': '',
                                # 添加数据库相关字段
                                'shop_name': shop_name,
                                'platform_name': platform_name,
                                'platform_channel': platform_channel
                            }

                            # 添加退货信息（如果有）
                            if return_info and len(return_info) > 0:
                                return_item = return_info[0]  # 取第一个退货信息
                                order_result['returnTrackingNumber'] = return_item.get('returnTrackingNumber', '')
                                order_result['returnShipmethod'] = return_item.get('ruturnShipmethod', '')  # API文档中有拼写错误

                            results.append(order_result)

                        # 更新成功计数
                        if account_type == 'sl':
                            self.sl_success_count += len(tracking_data)
                        else:
                            self.rw_success_count += len(tracking_data)

                        print(f"✅ 订单 {order_no} 查询完成")
                    else:
                        # 无追踪信息，记录为失败订单
                        failed_orders.append(order_data)
                        print(f"⚠️  订单 {order_no} 无追踪信息")
                else:
                    # API返回不成功，记录为失败订单
                    failed_orders.append(order_data)
                    print(f"❌ 订单 {order_no} API返回失败")
                
                print(f"✅ 订单 {order_no} 查询完成")

                # 每50个订单显示一次统计
                if i % 50 == 0:
                    success_count = len(results)
                    current_success_rate = (success_count / i) * 100 if i > 0 else 0
                    print(f"📊 {account_type.upper()}账号进度统计: {i}/{total_orders} 完成，成功率 {current_success_rate:.1f}% ({success_count}/{i})")

                # 添加延迟避免请求过于频繁
                time.sleep(0.3)
                
            except requests.exceptions.RequestException as e:
                print(f"❌ 订单 {order_no} 查询失败: {e}")
                failed_orders.append(order_data)
                continue

            # 添加延迟避免API限流
            time.sleep(0.3)

        return results, failed_orders

def read_order_numbers_from_excel(file_path):
    """从Excel文件A列读取订单号"""
    try:
        df = pd.read_excel(file_path)
        order_numbers = df.iloc[:, 0].dropna().astype(str).tolist()
        order_numbers = [order for order in order_numbers if order.strip()]
        print(f"📁 从 {file_path} 读取了 {len(order_numbers)} 个订单号")
        return order_numbers
    except Exception as e:
        print(f"❌ 读取Excel文件失败: {e}")
        raise

def normalize_order_number_for_matching(order_number):
    """标准化订单号用于匹配，移除末尾的后缀"""
    if not order_number:
        return order_number

    import re
    order_stripped = order_number.strip()

    # 只移除特定模式的后缀：
    # -1, -2 等单独的数字后缀
    # -R, -DF 等字母后缀
    # 但不移除正常订单号中间的数字部分
    normalized = re.sub(r'-([1-9]|R|DF)$', '', order_stripped)
    return normalized

def find_matching_order_in_database(feishu_order_number, orders_data):
    """在数据库订单中查找匹配的订单"""
    # 标准化飞书订单号
    normalized_feishu = normalize_order_number_for_matching(feishu_order_number)

    for order in orders_data:
        db_order_number = order['actual_order_number']
        normalized_db = normalize_order_number_for_matching(db_order_number)

        # 检查是否匹配（双向包含检查）
        if normalized_feishu == normalized_db or normalized_feishu in db_order_number or db_order_number in feishu_order_number:
            return order

    return None

def process_feishu_data_to_api_format(feishu_data, orders_data):
    """将飞书数据转换为API结果格式"""
    try:
        feishu_results = []

        # 创建订单号到平台信息的映射
        order_platform_map = {}
        for order in orders_data:
            order_number = order['actual_order_number']
            normalized_order = normalize_order_number_for_matching(order_number)

            platform_info = {
                'platform_channel': order.get('platform_channel', ''),
                'platform_id': order.get('platform_id', '')
            }

            # 同时存储原始订单号和标准化订单号的映射
            order_platform_map[order_number] = platform_info
            order_platform_map[normalized_order] = platform_info

        # 统计信息
        matched_count = 0
        unmatched_count = 0

        # 处理飞书数据
        for feishu_order_number, feishu_info in feishu_data.items():
            # 查找匹配的数据库订单
            matched_order = find_matching_order_in_database(feishu_order_number, orders_data)

            if matched_order:
                db_order_number = matched_order['actual_order_number']
                matched_count += 1

                # 为每个跟踪号创建一条记录
                for tracking_number in feishu_info['tracking_numbers']:
                    feishu_result = {
                        'orderNo': db_order_number,
                        'trackingNumber': tracking_number,
                        'carrierName': feishu_info['carrier'],
                        'status': 'SUCCESS',
                        'platform_channel': matched_order.get('platform_channel', ''),
                        'platform_id': matched_order.get('platform_id', ''),
                        'data_source': 'Feishu',
                        'feishu_original_order': feishu_order_number
                    }
                    feishu_results.append(feishu_result)
            else:
                unmatched_count += 1
                logger.warning(f"飞书订单 {feishu_order_number} 在数据库中未找到匹配订单")

        logger.info(f"飞书数据处理完成:")
        logger.info(f"  成功匹配订单: {matched_count}")
        logger.info(f"  未匹配订单: {unmatched_count}")
        logger.info(f"  生成记录数: {len(feishu_results)}")

        return feishu_results

    except Exception as e:
        logger.error(f"处理飞书数据失败: {e}")
        return []

def merge_feishu_data_to_api_results(api_results, feishu_data, orders_data):
    """合并飞书数据到API结果中，使用智能订单号匹配"""
    try:
        merged_results = list(api_results)  # 复制原有结果

        # 创建订单号到平台信息的映射（包括标准化匹配）
        order_platform_map = {}
        for order in orders_data:
            order_number = order['actual_order_number']
            normalized_order = normalize_order_number_for_matching(order_number)

            platform_info = {
                'platform_channel': order.get('platform_channel', ''),
                'platform_id': order.get('platform_id', ''),
                'shop_name': order.get('shop_name', '')
            }

            # 同时存储原始订单号和标准化订单号的映射
            order_platform_map[order_number] = platform_info
            order_platform_map[normalized_order] = platform_info

        # 统计信息
        feishu_added_count = 0
        feishu_enhanced_count = 0
        feishu_matched_count = 0
        feishu_unmatched_count = 0

        # 处理飞书数据
        for feishu_order_number, feishu_info in feishu_data.items():
            # 查找匹配的数据库订单
            matched_order = find_matching_order_in_database(feishu_order_number, orders_data)

            if matched_order:
                db_order_number = matched_order['actual_order_number']
                feishu_matched_count += 1

                # 检查该订单是否已经在API结果中
                existing_records = [r for r in merged_results if r.get('orderNo') == db_order_number]

                if existing_records:
                    # 订单已存在，检查是否有新的跟踪号
                    existing_tracking_numbers = set()
                    for record in existing_records:
                        tracking_num = record.get('trackingNumber', '')
                        if tracking_num and tracking_num not in ['N/A', 'ERROR']:
                            existing_tracking_numbers.add(tracking_num)

                    # 添加飞书中的新跟踪号
                    for tracking_number in feishu_info['tracking_numbers']:
                        if tracking_number not in existing_tracking_numbers:
                            new_record = {
                                'orderNo': db_order_number,
                                'trackingNumber': tracking_number,
                                'carrierName': feishu_info['carrier'],
                                'status': 'SUCCESS',
                                'platform_channel': matched_order.get('platform_channel', ''),
                                'platform_id': matched_order.get('platform_id', ''),
                                'shop_name': matched_order.get('shop_name', ''),
                                'data_source': 'Feishu',
                                'feishu_original_order': feishu_order_number  # 保留原始飞书订单号
                            }
                            merged_results.append(new_record)
                            feishu_enhanced_count += 1

                else:
                    # 订单不存在，添加所有飞书跟踪号
                    for tracking_number in feishu_info['tracking_numbers']:
                        new_record = {
                            'orderNo': db_order_number,
                            'trackingNumber': tracking_number,
                            'carrierName': feishu_info['carrier'],
                            'status': 'SUCCESS',
                            'platform_channel': matched_order.get('platform_channel', ''),
                            'platform_id': matched_order.get('platform_id', ''),
                            'shop_name': matched_order.get('shop_name', ''),
                            'data_source': 'Feishu',
                            'feishu_original_order': feishu_order_number  # 保留原始飞书订单号
                        }
                        merged_results.append(new_record)
                        feishu_added_count += 1
            else:
                # 未找到匹配的订单
                feishu_unmatched_count += 1
                logger.warning(f"飞书订单 {feishu_order_number} 在数据库中未找到匹配订单")

        logger.info(f"飞书数据合并完成:")
        logger.info(f"  飞书订单总数: {len(feishu_data)}")
        logger.info(f"  成功匹配订单: {feishu_matched_count}")
        logger.info(f"  未匹配订单: {feishu_unmatched_count}")
        logger.info(f"  新增订单跟踪号: {feishu_added_count}")
        logger.info(f"  增强现有订单: {feishu_enhanced_count}")
        logger.info(f"  合并后总记录数: {len(merged_results)}")

        return merged_results

    except Exception as e:
        logger.error(f"合并飞书数据失败: {e}")
        return api_results

def save_results_to_excel(api_results, original_order_numbers, filename):
    """将API结果保存到格式化的Excel文件（每个追踪号单独一行）"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f"{filename}_{timestamp}.xlsx"
    
    try:
        # 方法1：详细表 - 每个追踪号一行
        detailed_data = []
        
        # 按订单分组，统计每个订单的追踪号数量
        order_tracking_count = {}
        for result in api_results:
            order_no = result.get('orderNo', '')
            if order_no not in order_tracking_count:
                order_tracking_count[order_no] = 0
            if result.get('trackingNumber') not in ['N/A', 'ERROR', '']:
                order_tracking_count[order_no] += 1
        
        # 为每个追踪号创建详细记录
        row_counter = 1
        for result in api_results:
            order_no = result.get('orderNo', '')
            tracking_count = order_tracking_count.get(order_no, 0)
            
            row_data = {
                '行号': row_counter,
                '订单号': order_no,
                '该订单追踪号总数': tracking_count,
                '物流追踪号': result.get('trackingNumber', 'N/A'),
                '承运商': result.get('carrierName', 'N/A'),
                '退货追踪号': result.get('returnTrackingNumber', 'N/A'),
                '退货承运商': result.get('returnShipmethod', 'N/A'),
                '状态': result.get('status', 'N/A'),
                '查询时间': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                '错误信息': result.get('error', '')
            }
            detailed_data.append(row_data)
            row_counter += 1
        
        # 方法2：汇总表 - 每个订单一行，多追踪号合并显示
        summary_data = []
        order_groups = {}
        
        # 按订单号分组
        for result in api_results:
            order_no = result.get('orderNo', '')
            if order_no not in order_groups:
                order_groups[order_no] = []
            order_groups[order_no].append(result)
        
        # 为每个原始订单创建汇总行
        for idx, order_no in enumerate(original_order_numbers, 1):
            if order_no in order_groups:
                group = order_groups[order_no]
                
                # 收集所有追踪号
                tracking_numbers = []
                carrier_names = []
                for item in group:
                    tracking_num = item.get('trackingNumber', '')
                    if tracking_num not in ['N/A', 'ERROR', '']:
                        tracking_numbers.append(tracking_num)
                        carrier_name = item.get('carrierName', '')
                        if carrier_name not in ['N/A', 'ERROR', '']:
                            carrier_names.append(carrier_name)
                
                # 创建汇总行
                summary_row = {
                    '序号': idx,
                    '订单号': order_no,
                    '追踪号数量': len(tracking_numbers),
                    '所有追踪号': ' | '.join(tracking_numbers) if tracking_numbers else 'N/A',
                    '承运商': ' | '.join(set(carrier_names)) if carrier_names else 'N/A',
                    '退货追踪号': group[0].get('returnTrackingNumber', 'N/A'),
                    '状态': group[0].get('status', 'N/A'),
                    '查询时间': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    '错误信息': group[0].get('error', '')
                }
                summary_data.append(summary_row)
            else:
                # 未找到的订单
                summary_data.append({
                    '序号': idx,
                    '订单号': order_no,
                    '追踪号数量': 0,
                    '所有追踪号': 'NOT_FOUND',
                    '承运商': 'NOT_FOUND',
                    '退货追踪号': 'NOT_FOUND',
                    '状态': 'NOT_FOUND',
                    '查询时间': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    '错误信息': '未在查询结果中找到'
                })
        
        # 创建DataFrame
        detailed_df = pd.DataFrame(detailed_data)
        summary_df = pd.DataFrame(summary_data)
        
        # 统计信息
        total_orders = len(original_order_numbers)
        total_tracking_numbers = len([x for x in detailed_data if x['物流追踪号'] not in ['N/A', 'ERROR', '']])
        multi_tracking_orders = len([x for x in summary_data if x['追踪号数量'] > 1])
        
        # 创建Excel文件
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            # 详细表（主要表）- 每个追踪号一行
            detailed_df.to_excel(writer, sheet_name='所有追踪号详情', index=False)
            
            # 汇总表 - 每个订单一行
            summary_df.to_excel(writer, sheet_name='订单汇总', index=False)
            
            # 统计表
            stats_data = {
                '项目': [
                    '查询时间',
                    '总订单数',
                    '总追踪号数',
                    '有追踪号订单数',
                    '多追踪号订单数',
                    '平均每订单追踪号数'
                ],
                '值': [
                    datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    total_orders,
                    total_tracking_numbers,
                    len([x for x in summary_data if x['追踪号数量'] > 0]),
                    multi_tracking_orders,
                    f"{total_tracking_numbers/total_orders:.2f}" if total_orders > 0 else "0"
                ]
            }
            
            stats_df = pd.DataFrame(stats_data)
            stats_df.to_excel(writer, sheet_name='统计信息', index=False)
        
        print(f"💾 结果已保存到 {output_file}")
        print(f"📊 Excel文件包含:")
        print(f"   - 所有追踪号详情: {len(detailed_data)} 行记录（每个追踪号一行）")
        print(f"   - 订单汇总: {len(summary_data)} 行记录（每个订单一行）")
        print(f"   - 统计信息: 总体数据分析")
        print(f"📈 数据统计:")
        print(f"   - {total_orders} 个订单")
        print(f"   - {total_tracking_numbers} 个追踪号")
        print(f"   - {multi_tracking_orders} 个订单有多个追踪号")
        
        return output_file
        
    except Exception as e:
        print(f"❌ 保存Excel失败: {e}")
        raise

def test_carrier_codes():
    """测试承运商代码映射"""
    print("🔍 测试承运商代码映射...")
    test_carriers = ['FedEx', 'UPS', 'DHL Express', 'USPS', 'Unknown', '']
    for carrier in test_carriers:
        code = get_carrier_code(carrier)
        status = "✅" if code > 0 else "⚠️"
        print(f"  {status} '{carrier or '(空)'}' → {code}")

def main():
    """主函数"""
    import sys

    # 检查命令行参数
    if len(sys.argv) > 1 and sys.argv[1] == '--test':
        print("🧪 运行测试模式")
        print("=" * 50)

        # 测试承运商代码
        test_carrier_codes()

        # 测试数据库连接
        try:
            db_manager = DatabaseManager()
            print("\n� 测试数据库连接...")
            if db_manager.test_connection():
                print("✅ 数据库测试通过")
            else:
                print("❌ 数据库测试失败")
        except Exception as e:
            print(f"❌ 数据库测试错误: {e}")
        return

    print("�🚀 启动 GIGA 追踪号查询（数据库版本）")
    print("=" * 50)

    try:
        # 初始化数据库管理器和API客户端
        db_manager = DatabaseManager()
        api_client = GigaAPIClient()
        feishu_client = FeishuAPIClient()

        # 测试数据库连接
        print("🔍 检查数据库连接...")
        if not db_manager.test_connection():
            print("❌ 数据库连接失败，请检查配置")
            return

        # 🔧 优化：分别获取有跟踪号和无跟踪号的订单
        print("📊 从数据库获取订单信息...")

        # 获取所有订单（用于统计和报告）
        all_orders_data = db_manager.get_orders_from_database(include_with_tracking=True)

        # 获取需要查询的订单（无跟踪号的订单）
        orders_without_tracking = db_manager.get_orders_from_database(include_with_tracking=False)

        if not all_orders_data:
            print("❌ 未找到订单数据")
            return

        orders_with_tracking_count = len(all_orders_data) - len(orders_without_tracking)

        print(f"📋 订单统计:")
        print(f"   总订单数: {len(all_orders_data)} 个")
        print(f"   已有跟踪号: {orders_with_tracking_count} 个（跳过查询）")
        print(f"   需要查询: {len(orders_without_tracking)} 个")

        # 🔧 优化：优先获取飞书表格数据
        print("📋 第一步：获取飞书表格数据...")
        feishu_data = feishu_client.get_all_feishu_records()
        feishu_results = []

        if feishu_data:
            print(f"✅ 飞书表格获取成功，找到 {len(feishu_data)} 个订单的跟踪信息")

            # 将飞书数据转换为API结果格式
            print("🔄 处理飞书数据...")
            feishu_results = process_feishu_data_to_api_format(feishu_data, orders_without_tracking)
            print(f"✅ 飞书数据处理完成，获得 {len(feishu_results)} 条记录")

            # 从需要查询的订单中移除已在飞书中找到的订单
            feishu_order_numbers = set()
            for result in feishu_results:
                feishu_order_numbers.add(result.get('orderNo'))

            remaining_orders = [order for order in orders_without_tracking
                              if order['actual_order_number'] not in feishu_order_numbers]

            print(f"   📋 飞书中找到: {len(feishu_order_numbers)} 个订单")
            print(f"   🔍 仍需GIGA查询: {len(remaining_orders)} 个订单")
        else:
            print("⚠️ 飞书表格数据获取失败或为空")
            remaining_orders = orders_without_tracking

        # 🔧 优化：只对剩余订单查询GIGA API
        giga_results = []
        if remaining_orders:
            print(f"🔍 第二步：查询GIGA API追踪号（{len(remaining_orders)} 个订单）...")
            giga_results = api_client.query_tracking_numbers(remaining_orders)
        else:
            print("✅ 所有订单都已在飞书中找到，跳过GIGA API查询")

        # 🔧 合并所有结果
        print("🔄 合并所有数据源...")
        all_results = feishu_results + giga_results
        print(f"✅ 数据合并完成，总计 {len(all_results)} 条记录")
        print(f"   📋 飞书来源: {len(feishu_results)} 条")
        print(f"   📋 GIGA来源: {len(giga_results)} 条")

        # 更新orders表（使用优化后的全局分组逻辑和合并后的数据）
        print("💾 更新orders表追踪信息...")
        updated_count = db_manager.update_orders_with_tracking(all_results)

        # 同时保存Excel报告（可选）
        print("📄 生成Excel报告...")
        order_numbers = [order['actual_order_number'] for order in all_orders_data]
        output_file = save_results_to_excel(all_results, order_numbers, "tracking_results")

        # 🔧 新增：基于Excel报告修复多跟踪号订单
        print("🔧 检查并修复多跟踪号订单...")
        fixed_count = db_manager.fix_multi_tracking_from_excel(output_file)
        if fixed_count > 0:
            print(f"   ✅ 修复了 {fixed_count} 个多跟踪号订单")
        else:
            print("   ✅ 没有需要修复的多跟踪号订单")

        # 🔍 新增：数据一致性验证
        print("🔍 验证数据一致性...")
        validation_result = db_manager.validate_data_consistency()
        if validation_result:
            if validation_result['quantity_issues'] == 0 and validation_result['content_issues'] == 0:
                print("   ✅ 数据一致性验证通过")
            else:
                print(f"   ⚠️ 发现 {validation_result['quantity_issues']} 个数量问题，{validation_result['content_issues']} 个内容问题")

            print(f"   📊 最终统计: {validation_result['total_orders']} 个订单，{validation_result['multi_tracking_orders']} 个多跟踪号订单")

        print(f"✅ 完成!")
        print(f"   - 已更新 {updated_count} 个订单的追踪信息到orders表")
        print(f"   - 修复了 {fixed_count} 个多跟踪号订单")
        print(f"   - Excel报告: {output_file}")
        print(f"   - 数据一致性: {'✅ 正常' if validation_result and validation_result['quantity_issues'] == 0 and validation_result['content_issues'] == 0 else '⚠️ 有问题'}")

    except Exception as e:
        logger.error(f"程序执行错误: {e}")
        print(f"❌ 错误: {e}")

if __name__ == "__main__":
    main()
