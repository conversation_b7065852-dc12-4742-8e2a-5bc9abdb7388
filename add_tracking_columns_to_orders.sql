-- 为orders表添加追踪相关字段的SQL脚本
-- 这些字段将存储从GIGA API查询到的追踪信息

-- 添加追踪号字段
ALTER TABLE `orders` 
ADD COLUMN `tracking_number` varchar(100) DEFAULT NULL COMMENT '追踪号' AFTER `platform_id`;

-- 添加承运商代码字段
ALTER TABLE `orders` 
ADD COLUMN `carrier_code` int(11) DEFAULT 0 COMMENT '承运商代码' AFTER `tracking_number`;

-- 添加状态字段
ALTER TABLE `orders` 
ADD COLUMN `status` varchar(50) DEFAULT NULL COMMENT '查询状态' AFTER `carrier_code`;

-- 添加最新事件描述字段
ALTER TABLE `orders` 
ADD COLUMN `latest_event_description` text DEFAULT NULL COMMENT '最新事件描述' AFTER `status`;

-- 添加最新事件位置字段
ALTER TABLE `orders` 
ADD COLUMN `latest_event_location` varchar(200) DEFAULT NULL COMMENT '最新事件位置' AFTER `latest_event_description`;

-- 添加最新事件时间字段
ALTER TABLE `orders` 
ADD COLUMN `latest_event_time` timestamp NULL DEFAULT NULL COMMENT '最新事件时间' AFTER `latest_event_location`;

-- 添加备注字段
ALTER TABLE `orders` 
ADD COLUMN `remark` text DEFAULT NULL COMMENT '备注' AFTER `latest_event_time`;

-- 添加是否未邀请评价字段
ALTER TABLE `orders` 
ADD COLUMN `is_uninvited_review` tinyint(1) DEFAULT 0 COMMENT '是否未邀请评价' AFTER `remark`;

-- 添加完整追踪信息字段
ALTER TABLE `orders` 
ADD COLUMN `full_track_info` longtext DEFAULT NULL COMMENT '完整追踪信息JSON' AFTER `is_uninvited_review`;

-- 添加索引以提高查询性能
ALTER TABLE `orders` 
ADD INDEX `idx_tracking_number` (`tracking_number`),
ADD INDEX `idx_carrier_code` (`carrier_code`),
ADD INDEX `idx_status` (`status`);

-- 显示添加的字段
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'orders' 
  AND COLUMN_NAME IN (
    'tracking_number', 
    'carrier_code', 
    'status', 
    'latest_event_description', 
    'latest_event_location', 
    'latest_event_time', 
    'remark', 
    'is_uninvited_review', 
    'full_track_info'
  )
ORDER BY ORDINAL_POSITION;
