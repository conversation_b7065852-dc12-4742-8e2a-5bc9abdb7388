#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
查询指定订单的跟踪号信息
"""

import os
import urllib.parse
from dotenv import load_dotenv
from sqlalchemy import create_engine, text
import sys

def query_order_tracking(order_number):
    """查询订单的跟踪号信息"""
    
    # 加载环境变量
    load_dotenv()
    
    # 数据库连接配置
    db_host = os.getenv('DB_HOST', 'localhost')
    db_port = int(os.getenv('DB_PORT', 3306))
    db_user = os.getenv('DB_USER')
    db_password = os.getenv('DB_PASSWORD')
    db_name = os.getenv('DB_NAME')
    
    if not all([db_user, db_password, db_name]):
        print("❌ 数据库连接信息不完整，请检查.env文件")
        return
    
    # 创建数据库连接
    encoded_password = urllib.parse.quote_plus(db_password)
    connection_string = f"mysql+pymysql://{db_user}:{encoded_password}@{db_host}:{db_port}/{db_name}"
    engine = create_engine(connection_string, echo=False)
    
    try:
        with engine.connect() as conn:
            print(f"🔍 查询订单: {order_number}")
            print("=" * 60)
            
            # 1. 查询orders主表
            print("📋 主表 (orders) 信息:")
            result = conn.execute(text("""
                SELECT order_number, platform_channel, platform_id, 
                       tracking_number, tracking_count, carrier_code, 
                       tracking_source, tracking_last_updated
                FROM orders 
                WHERE order_number = :order_no OR platform_id = :order_no
            """), {'order_no': order_number})
            
            main_record = result.fetchone()
            if main_record:
                print(f"   📦 订单号: {main_record[0]}")
                print(f"   🏪 平台渠道: {main_record[1] or 'N/A'}")
                print(f"   🆔 平台ID: {main_record[2] or 'N/A'}")
                print(f"   📋 跟踪号: {main_record[3] or 'N/A'}")
                print(f"   🔢 跟踪号数量: {main_record[4] or 0}")
                print(f"   🚚 承运商代码: {main_record[5] or 0}")
                print(f"   📡 数据来源: {main_record[6] or 'N/A'}")
                print(f"   ⏰ 最后更新: {main_record[7] or 'N/A'}")
                
                # 解析跟踪号
                tracking_numbers = []
                if main_record[3]:
                    # 支持多种分隔符
                    tracking_str = main_record[3]
                    for sep in [';', ',', '|', '\n']:
                        if sep in tracking_str:
                            tracking_numbers = [num.strip() for num in tracking_str.split(sep)]
                            break
                    else:
                        tracking_numbers = [tracking_str.strip()]
                
                # 过滤空值
                tracking_numbers = [num for num in tracking_numbers if num]
                
                print(f"\n📦 主表中的跟踪号详情 ({len(tracking_numbers)}个):")
                if tracking_numbers:
                    for i, num in enumerate(tracking_numbers, 1):
                        print(f"   {i}. {num}")
                else:
                    print("   ❌ 无跟踪号")
                    
            else:
                print("   ❌ 在主表中未找到该订单")
            
            # 2. 查询order_tracking_details详情表
            print(f"\n📋 详情表 (order_tracking_details) 信息:")
            result = conn.execute(text("""
                SELECT order_number, tracking_number, carrier_code, carrier_name,
                       status, data_source, created_at, updated_at
                FROM order_tracking_details 
                WHERE order_number = :order_no
                ORDER BY created_at
            """), {'order_no': order_number})
            
            detail_records = result.fetchall()
            if detail_records:
                print(f"   📊 找到 {len(detail_records)} 条详情记录:")
                for i, record in enumerate(detail_records, 1):
                    print(f"\n   📦 记录 {i}:")
                    print(f"      🏷️  跟踪号: {record[1]}")
                    print(f"      🚚 承运商代码: {record[2]}")
                    print(f"      🏢 承运商名称: {record[3] or 'N/A'}")
                    print(f"      📊 状态: {record[4] or 'N/A'}")
                    print(f"      📡 数据来源: {record[5] or 'N/A'}")
                    print(f"      📅 创建时间: {record[6]}")
                    print(f"      🔄 更新时间: {record[7]}")
            else:
                print("   ❌ 在详情表中未找到该订单")
            
            # 3. 数据一致性检查
            print(f"\n📊 数据一致性检查:")
            if main_record:
                main_tracking_count = main_record[4] or 0
                detail_count = len(detail_records)
                main_tracking_numbers = []
                
                if main_record[3]:
                    tracking_str = main_record[3]
                    for sep in [';', ',', '|', '\n']:
                        if sep in tracking_str:
                            main_tracking_numbers = [num.strip() for num in tracking_str.split(sep)]
                            break
                    else:
                        main_tracking_numbers = [tracking_str.strip()]
                    main_tracking_numbers = [num for num in main_tracking_numbers if num]
                
                print(f"   📋 主表显示跟踪号数量: {main_tracking_count}")
                print(f"   📋 主表实际跟踪号数量: {len(main_tracking_numbers)}")
                print(f"   📋 详情表记录数: {detail_count}")
                
                # 检查跟踪号是否一致
                detail_tracking_numbers = [record[1] for record in detail_records]
                
                if set(main_tracking_numbers) == set(detail_tracking_numbers):
                    print("   ✅ 跟踪号一致性: 正常")
                else:
                    print("   ⚠️ 跟踪号一致性: 异常")
                    
                    # 显示差异
                    main_set = set(main_tracking_numbers)
                    detail_set = set(detail_tracking_numbers)
                    
                    only_in_main = main_set - detail_set
                    only_in_detail = detail_set - main_set
                    
                    if only_in_main:
                        print(f"      🔸 仅在主表中: {list(only_in_main)}")
                    if only_in_detail:
                        print(f"      🔸 仅在详情表中: {list(only_in_detail)}")
                
                if main_tracking_count == len(main_tracking_numbers) == detail_count:
                    print("   ✅ 数量一致性: 正常")
                else:
                    print("   ⚠️ 数量一致性: 异常")
                    
            else:
                print("   ❌ 该订单不存在于数据库中")
            
            # 4. 总结
            print(f"\n🎯 查询总结:")
            if main_record:
                tracking_count = len(tracking_numbers) if main_record else 0
                detail_count = len(detail_records)
                
                print(f"   📦 订单存在: ✅")
                print(f"   📋 跟踪号总数: {tracking_count}")
                print(f"   📊 存储位置:")
                print(f"      - orders表: {tracking_count}个跟踪号")
                print(f"      - order_tracking_details表: {detail_count}条记录")
                
                if tracking_count > 1:
                    print(f"   🎯 这是一个多跟踪号订单！")
                elif tracking_count == 1:
                    print(f"   📦 这是一个单跟踪号订单")
                else:
                    print(f"   ❌ 该订单没有跟踪号")
            else:
                print(f"   📦 订单存在: ❌")
                print(f"   💡 建议: 检查订单号是否正确，或者该订单是否已被查询过")
                
    except Exception as e:
        print(f"❌ 查询失败: {e}")

def main():
    """主函数"""
    if len(sys.argv) != 2:
        print("使用方法: python query_order.py <订单号>")
        print("示例: python query_order.py 114-6384775-2617861")
        return
    
    order_number = sys.argv[1]
    query_order_tracking(order_number)

if __name__ == "__main__":
    main()
