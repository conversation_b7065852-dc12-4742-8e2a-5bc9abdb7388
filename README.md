# GIGA Cloud Logistics 追踪号查询工具（数据库版本）

## 📋 功能说明
- 从MySQL数据库的orders表读取订单信息
- 支持eBay平台特殊订单号处理（使用platform_id）
- 调用GIGA Cloud Logistics API查询追踪号
- 支持多追踪号订单（每个追踪号分开显示）
- 将追踪信息保存到MySQL数据库的tracking表
- 同时生成详细的Excel报告

## 🚀 快速使用

### 1. 数据库准备
- 创建MySQL数据库和相关表
- 运行 `create_tracking_table.sql` 创建tracking表
- 确保orders表包含必要字段：order_number, platform_channel, platform_id, shop_name, platform_name

### 2. 配置文件
- 创建 `.env` 文件，包含以下内容：
```bash
# SL账号 GIGA API 配置（优先使用）
SL_CLIENT_ID=your_sl_client_id_here
SL_CLIENT_SECRET=your_sl_client_secret_here

# RW账号 GIGA API 配置（备用）
RW_CLIENT_ID=your_rw_client_id_here
RW_CLIENT_SECRET=your_rw_client_secret_here

# 兼容原有配置（如果没有配置RW_开头的，会使用这些）
CLIENT_ID=your_client_id_here
CLIENT_SECRET=your_client_secret_here

# MySQL 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=your_database_username
DB_PASSWORD=your_database_password
DB_NAME=your_database_name
```
- 根据需要修改 `carrier_codes.py` 中的承运商代码映射

### 3. 安装依赖
```bash
# 激活虚拟环境
source myenv/bin/activate

# 安装新依赖
pip install -r requirements.txt
```

### 4. 测试和运行
```bash
# 测试数据库连接和承运商代码映射
python track_orders.py --test

# 运行完整程序
NO_PROXY="*" python track_orders.py
```

### 5. 输出结果
- **数据库**：追踪信息保存到tracking表
- **Excel文件**：生成 `tracking_results_YYYYMMDD_HHMMSS.xlsx` 报告，包含：
  - **所有追踪号详情**：每个追踪号一行
  - **订单汇总**：每个订单一行，多追踪号用"|"分隔
  - **统计信息**：总体数据分析

## 📁 文件说明
- `track_orders.py` - 主脚本（包含所有功能和测试）
- `carrier_codes.py` - 承运商代码映射配置
- `create_tracking_table.sql` - 创建tracking表的SQL脚本
- `requirements.txt` - 依赖包列表
- `.env` - 环境变量配置文件（需要创建）
- `myenv/` - Python虚拟环境

## ⚙️ 环境要求
- Python 3.7+
- MySQL 5.7+ 或 MariaDB 10.2+
- 已安装的依赖包：requests, pandas, python-dotenv, openpyxl, pymysql, sqlalchemy

## 🗄️ 数据库表结构

### orders表（实际字段）
- `order_number` - 订单号
- `platform_channel` - 平台名称（如：eBay, Amazon等）
- `platform_id` - 平台订单ID（eBay平台使用此字段作为订单号）
- `store_account` - 店铺名称

### tracking表（自动创建）
- `order_number` - 订单号
- `tracking_number` - 追踪号
- `carrier_name` - 承运商名称
- `carrier_code` - 承运商数字代码
- `shop_name` - 店铺名称
- `platform_name` - 平台名称
- `platform_channel` - 平台渠道
- 其他字段...
