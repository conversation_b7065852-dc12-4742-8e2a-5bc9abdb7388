#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
承运商代码映射配置文件
用于将承运商名称映射到对应的数字代码
"""

# 承运商代码映射字典
# 格式: '承运商名称': 数字代码
CARRIER_CODE_MAPPING = {
    # 根据用户提供的承运商代码映射
    'FedEx': 100003,
    'CEVA Logistics': 100297,
    'Metropolitan': 100929,
    'Estes': 100221,
    'Estafeta USA': 100819,
    'FedEx France Domestic': 100854,
    'Amazon Shipping + Amazon MCF': 100308,
    'UPS': 100002,
    'USPS': 21051,
    'DHL ACTIVETRACING': 100216,
    'DHL eCommerce Asia': 7048,
    'DHL eCommerce CN': 100765,
    'DHL eCommerce US': 7047,
    'DHL Express': 100001,
    'DHL Freight': 100245,
    'DHL Global Forwarding': 100766,
    'DHL Paket': 7041,

    # 常见的承运商名称变体（可能在API返回中出现）
    'DHL': 100001,  # 默认映射到DHL Express
    'FedEx Ground': 100003,  # 映射到FedEx
    'FedEx Express': 100003,  # 映射到FedEx
    'FEDEX-HME': 100003,  # 飞书表格中的FedEx变体
    'FEDEX-W003': 100003,  # 飞书表格中的FedEx变体
    'UPS Ground': 100002,  # 映射到UPS
    'UPS Express': 100002,  # 映射到UPS
    'USPS Priority Mail': 21051,  # 映射到USPS
    'USPS Ground Advantage': 21051,  # 映射到USPS

    # CEVA承运商变体（飞书表格中的CEVA变体）
    'CEVA': 100297,  # 映射到CEVA Logistics

    # GigaCloud承运商变体
    'GigaCloud Technology Inc': 101034,  # GigaCloud完整名称
    'Giga': 101034,  # GigaCloud简称

    # 默认未知承运商
    'Unknown': 0,
    '': 0,  # 空字符串
    'N/A': 0,
    'ERROR': 0
}

def get_carrier_code(carrier_name):
    """
    根据承运商名称获取对应的数字代码
    
    Args:
        carrier_name (str): 承运商名称
        
    Returns:
        int: 对应的数字代码，如果未找到则返回0
    """
    if not carrier_name:
        return 0
    
    # 先尝试精确匹配
    if carrier_name in CARRIER_CODE_MAPPING:
        return CARRIER_CODE_MAPPING[carrier_name]
    
    # 尝试不区分大小写的匹配
    carrier_name_lower = carrier_name.lower()
    for name, code in CARRIER_CODE_MAPPING.items():
        if name.lower() == carrier_name_lower:
            return code
    
    # 尝试部分匹配（包含关系）
    for name, code in CARRIER_CODE_MAPPING.items():
        if name.lower() in carrier_name_lower or carrier_name_lower in name.lower():
            return code
    
    # 如果都没有匹配到，返回0（未知）
    return 0

def add_carrier_mapping(carrier_name, carrier_code):
    """
    添加新的承运商映射
    
    Args:
        carrier_name (str): 承运商名称
        carrier_code (int): 承运商代码
    """
    CARRIER_CODE_MAPPING[carrier_name] = carrier_code

def get_all_mappings():
    """
    获取所有承运商映射

    Returns:
        dict: 所有承运商映射字典
    """
    return CARRIER_CODE_MAPPING.copy()

def get_carrier_name_by_code(carrier_code):
    """
    根据承运商代码获取对应的名称

    Args:
        carrier_code (int): 承运商代码

    Returns:
        str: 承运商名称
    """
    # 承运商代码到名称的映射
    code_to_name = {
        100001: 'DHL Express',
        100002: 'UPS',
        100003: 'FedEx',
        100216: 'DHL ACTIVETRACING',
        100221: 'Estes',
        100245: 'DHL Freight',
        100297: 'CEVA Logistics',
        100308: 'Amazon Shipping + Amazon MCF',
        100765: 'DHL eCommerce CN',
        100766: 'DHL Global Forwarding',
        100819: 'Estafeta USA',
        100854: 'FedEx France Domestic',
        100929: 'Metropolitan',
        101034: 'GigaCloud Technology Inc',
        21051: 'USPS',
        7041: 'DHL Paket',
        7047: 'DHL eCommerce US',
        7048: 'DHL eCommerce Asia',
        0: 'Unknown'
    }

    return code_to_name.get(carrier_code, f'Unknown ({carrier_code})')

if __name__ == "__main__":
    # 测试代码
    print("承运商代码映射测试:")
    test_carriers = ['DHL', 'FedEx', 'USPS', 'Unknown Carrier', '']
    
    for carrier in test_carriers:
        code = get_carrier_code(carrier)
        print(f"  {carrier or '(空)'} -> {code}")
